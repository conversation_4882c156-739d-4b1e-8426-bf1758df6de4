import { Response } from "express";
import { formatResponse } from "../utilities/formatRes";
import { handleHotelAssign } from "../utilities/handleHotelAssign";
import Logger from "../../utils/logUtils";
import { Booking, PaymentMethod } from "../model/booking.model";
import { Transaction } from "../model/transaction.model";
import { 
  makePaymentSchema, 
  getPaymentHistorySchema, 
  getBookingBalanceSchema,
  MakePaymentInput 
} from "../../zod/bookingPaymentSchema";
import { AuthenticatedRequest } from "../types/common";

export const bookingPaymentController = {
  // Make a payment for a booking
  makePayment: async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Validate request body
      const validatedData: MakePaymentInput = await makePaymentSchema.parseAsync(req.body);
      const { bookingId, amount, paymentMethod, notes } = validatedData;
      
      const hotel = await handleHotelAssign(req);

      // Find the booking and verify it belongs to the hotel
      const booking = await Booking.findOne({
        _id: bookingId,
        hotel: hotel.id
      }).populate('guest').populate('room');

      if (!booking) {
        return formatResponse(res, 404, false, "Booking not found");
      }

      // Calculate remaining balance
      const remainingBalance = booking.amount - booking.amountPaid;

      // Validate payment amount
      if (amount > remainingBalance) {
        return formatResponse(
          res, 
          400, 
          false, 
          `Payment amount (${amount}) exceeds remaining balance (${remainingBalance})`
        );
      }

      if (remainingBalance <= 0) {
        return formatResponse(res, 400, false, "Booking is already fully paid");
      }

      // Update the booking with new payment
      const newAmountPaid = booking.amountPaid + amount;
      
      const updatedBooking = await Booking.findByIdAndUpdate(
        bookingId,
        {
          amountPaid: newAmountPaid,
          paymentMethod: paymentMethod as PaymentMethod,
          // Payment status will be automatically updated by the booking middleware
        },
        { new: true }
      ).populate('guest').populate('room').populate('roomType');

      if (!updatedBooking) {
        return formatResponse(res, 500, false, "Failed to update booking payment");
      }

      Logger.info(`Payment processed for booking: ${booking.bookingId}`, {
        userId: req.user?._id,
        username: req.user?.name,
        bookingId: booking.bookingId,
        amount,
        paymentMethod,
        action: "process_booking_payment"
      });

      // Return success response with updated booking
      return formatResponse(
        res, 
        200, 
        true, 
        "Payment processed successfully", 
        {
          booking: updatedBooking,
          paymentDetails: {
            amountPaid: amount,
            paymentMethod,
            newTotalPaid: newAmountPaid,
            remainingBalance: updatedBooking.amount - newAmountPaid,
            paymentStatus: updatedBooking.paymentStatus,
            notes
          }
        }
      );

    } catch (error: any) {
      Logger.error(`Error processing booking payment: ${error.message}`, {
        userId: req.user?._id,
        username: req.user?.name,
        error: error.message,
        stack: error.stack
      });

      if (error.name === 'ZodError') {
        return formatResponse(res, 400, false, "Validation error", error.errors);
      }

      return formatResponse(res, 500, false, "Error processing payment", error.message);
    }
  },

  // Get payment history for a booking
  getPaymentHistory: async (req: AuthenticatedRequest, res: Response) => {
    try {
      const validatedData = await getPaymentHistorySchema.parseAsync(req.params);
      const { bookingId } = validatedData;
      
      const hotel = await handleHotelAssign(req);

      // Verify booking exists and belongs to hotel
      const booking = await Booking.findOne({
        _id: bookingId,
        hotel: hotel.id
      }).select('bookingId amount amountPaid paymentStatus transactions');

      if (!booking) {
        return formatResponse(res, 404, false, "Booking not found");
      }

      // Get all transactions for this booking
      const transactions = await Transaction.find({
        booking: bookingId
      }).sort({ date: -1 }).populate('guest', 'name email');

      return formatResponse(
        res, 
        200, 
        true, 
        "Payment history retrieved successfully", 
        {
          booking: {
            bookingId: booking.bookingId,
            totalAmount: booking.amount,
            amountPaid: booking.amountPaid,
            remainingBalance: booking.amount - booking.amountPaid,
            paymentStatus: booking.paymentStatus
          },
          transactions
        }
      );

    } catch (error: any) {
      Logger.error(`Error retrieving payment history: ${error.message}`, {
        userId: req.user?._id,
        username: req.user?.name,
        error: error.message
      });

      if (error.name === 'ZodError') {
        return formatResponse(res, 400, false, "Validation error", error.errors);
      }

      return formatResponse(res, 500, false, "Error retrieving payment history", error.message);
    }
  },

  // Get booking balance information
  getBookingBalance: async (req: AuthenticatedRequest, res: Response) => {
    try {
      const validatedData = await getBookingBalanceSchema.parseAsync(req.params);
      const { bookingId } = validatedData;
      
      const hotel = await handleHotelAssign(req);

      // Find booking and get balance information
      const booking = await Booking.findOne({
        _id: bookingId,
        hotel: hotel.id
      }).select('bookingId amount amountPaid paymentStatus paymentMethod')
        .populate('guest', 'name email');

      if (!booking) {
        return formatResponse(res, 404, false, "Booking not found");
      }

      const remainingBalance = booking.amount - booking.amountPaid;

      return formatResponse(
        res, 
        200, 
        true, 
        "Booking balance retrieved successfully", 
        {
          bookingId: booking.bookingId,
          guest: booking.guest,
          totalAmount: booking.amount,
          amountPaid: booking.amountPaid,
          remainingBalance,
          paymentStatus: booking.paymentStatus,
          paymentMethod: booking.paymentMethod,
          isFullyPaid: remainingBalance <= 0
        }
      );

    } catch (error: any) {
      Logger.error(`Error retrieving booking balance: ${error.message}`, {
        userId: req.user?._id,
        username: req.user?.name,
        error: error.message
      });

      if (error.name === 'ZodError') {
        return formatResponse(res, 400, false, "Validation error", error.errors);
      }

      return formatResponse(res, 500, false, "Error retrieving booking balance", error.message);
    }
  }
};
