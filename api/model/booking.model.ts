import {model, Schema, Types} from "mongoose";
import constants from "../constants";
import User from "./user.model";
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import {sendNewUserEmail} from "../utilities/sendMail";
import {createTransaction} from "../utilities/createTrans";
import {createFinancialTransaction} from "../utilities/createFinancialTransaction";
import {PaymentMethod as FinancialPaymentMethod, TransactionType} from "../model/financialTransaction.model";
import {checkAndUpgradeMembership} from "./membership.model";
import {roomStatusService} from "../services/roomStatusService";
import {Transaction} from "./transaction.model";

export enum PaymentStatus {
    PARTIALLY_PAID = "partially-paid",
    FULLY_PAID = "fully-paid",
    UNPAID = "unpaid",
}

export interface IPackageInfo {
    package: Types.ObjectId;
    inclusions: string[];
    discount: number;
}

export interface IPaxDetails {
    adults: number;
    children: number;
    infants?: number;
}

export enum PaymentMethod {
    CASH = "cash",
    ONLINE = "online"
}

export enum BookingStatus {
    PENDING = "pending",
    CONFIRMED = "confirmed",
    CHECKED_IN = "checked-in",
    CHECKED_OUT = "checked-out",
    CANCELLED = "cancelled",
    NO_SHOW = "no-show",
}

export interface IBooking extends Document {
    bookingId: string;
    reservationDate: Date;
    hotel: Types.ObjectId;
    roomType: Types.ObjectId;
    room: Types.ObjectId;
    guest: Types.ObjectId;
    checkIn: Date;
    checkOut: Date;
    expectedCheckOut: Date;
    package?: IPackageInfo;
    paymentStatus: PaymentStatus;
    paymentMethod: PaymentMethod;
    amount: number;
    amountPaid: number;

    pax: IPaxDetails;
    specialRequests?: string;
    cancellationDate?: Date;
    cancellationReason?: string;
    status: BookingStatus;
    isConfirmed: boolean;
    durationNights: number;
    usedServices: {
        service: Types.ObjectId;
        paidAmount: number;
        date: Date;
    }[];
    membershipDiscount?: number;
    membershipDiscountAmount?: number;
    originalAmount: number;
    bookedActivities: Types.ObjectId[];
    transactions: Types.ObjectId[];
    orders: Types.ObjectId[];
    createdBy?: Types.ObjectId;
    updatedBy?: Types.ObjectId;
}

const BookingSchema = new Schema<IBooking>(
    {
        bookingId: {
            type: String,
            unique: true,
            index: true,
        },
        reservationDate: {
            type: Date,
            default: Date.now,
        },
        roomType: {
            type: Schema.Types.ObjectId,
        },
        hotel: {
            type: Schema.Types.ObjectId,
            ref: constants.DB.HOTEL,
            required: true,
            index: true,
        },
        membershipDiscount: {
            type: Number,
            min: 0,
            max: 100,
            default: 0
        },
        membershipDiscountAmount: {
            type: Number,
            min: 0,
            default: 0
        },
        originalAmount: {
            type: Number,
            min: 0
        },
        room: {
            type: Schema.Types.ObjectId,
            ref: constants.DB.ROOM,
            required: true,
            index: true,
        },
        guest: {
            type: Schema.Types.Mixed,
            ref: constants.DB.USER,
            required: true,
            index: true,
        },
        checkIn: {
            type: Date,
        },
        checkOut: {
            type: Date,
            // validate: {
            //     validator: function (this: IBooking, value: Date) {
            //         return value > this.checkIn;
            //     },
            //     message: "Check-out must be after check-in",
            // },
            index: true,
        },
        expectedCheckOut: {
            type: Date,
        },
        package: {
            package: {
                type: Schema.Types.ObjectId,
                ref: constants.DB.PACKAGE,
            },
            discount: {
                type: Number,
                min: 0,
                max: 100,
                default: 0,
            },
        },
        paymentStatus: {
            type: String,
            enum: Object.values(PaymentStatus),
            default: PaymentStatus.UNPAID,
            index: true,
        },
        paymentMethod: {
            type: String,
            enum: Object.values(PaymentMethod),
        },
        amount: {
            type: Number,
            required: true,
            min: 0,
        },
        amountPaid: {
            type: Number,
            default: 0,
            min: 0,
        },
        pax: {
            adults: {
                type: Number,
                required: true,
                min: 1,
                max: 10,
            },
            children: {
                type: Number,
                default: 0,
                min: 0,
                max: 10,
            },
            infants: {
                type: Number,
                default: 0,
                min: 0,
                max: 5,
            },
        },
        specialRequests: String,
        cancellationDate: Date,
        cancellationReason: String,
        status: {
            type: String,
            enum: Object.values(BookingStatus),
            default: BookingStatus.PENDING,
            index: true,
        },
        isConfirmed: {
            type: Boolean,
            default: true,
            index: true,
        },
        durationNights: {
            type: Number,
            min: 1,
        },
        usedServices: [{
            service: {
                type: Schema.Types.ObjectId,
                ref: constants.DB.SERVICE,
            },
            price: Number
        }],
        transactions: [{
            type: Schema.Types.ObjectId,
            ref: `Guest_${constants.DB.TRANSACTION}`,
        }],
        bookedActivities: [{
            type: Schema.Types.ObjectId,
            ref: constants.DB.ACTIVITY,
        }],
        orders: [{
            type: Schema.Types.ObjectId,
            ref: constants.DB.ORDER,
        }],
        createdBy: {
            type: Schema.Types.ObjectId,
            ref: constants.DB.USER,
        },
        updatedBy: {
            type: Schema.Types.ObjectId,
            ref: constants.DB.USER,
        },
    },
    {
        timestamps: true,
        toJSON: {virtuals: true},
        toObject: {virtuals: true},
    }
);

// OPTIMIZATION 1: Selective Population Strategy
// Only populate essential fields by default, create separate methods for full population
BookingSchema.pre(["find", "findOne"], function (next: any) {
    // Check if population is explicitly disabled via query options
    if (this.getQuery()._noPopulate) {
        delete this.getQuery()._noPopulate;
        return next();
    }

    // Determine population level from query options or default to basic
    const populateLevel = this.getQuery()._populateLevel || 'basic';
    delete this.getQuery()._populateLevel;

    // Basic population (default, faster)
    if (populateLevel === 'basic') {
        this.populate([
            {path: "room", select: "name roomNo status"},
            {path: "guest", select: "name email phoneNumber"}
        ]);
    }
    // Full population (slower, use only when needed)
    else if (populateLevel === 'full') {
        this.populate([
            {path: "createdBy"},
            {path: "updatedBy"},
            {path: "package"},
            {path: "room"},
            {path: "guest"},
            {path: "usedServices"},
            {path: "bookedActivities"},
        ]).sort({createdAt: -1});
    }
    next();
});

// OPTIMIZATION 2: Efficient Virtual Property
BookingSchema.virtual("calculatedDurationNights").get(function (this: IBooking) {
    if (!this.checkIn || !this.checkOut) return 0;
    return Math.ceil(
        (this.checkOut?.getTime() - this.checkIn?.getTime()) / (1000 * 3600 * 24)
    );
});

// OPTIMIZATION 3: More efficient booking ID generation with less database queries
// Rewritten to eliminate collision detection queries and reduce complexity
const generateBookingId = async function (this: any) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    // Use timestamp to ensure uniqueness without requiring lookup
    const timestamp = Date.now().toString().slice(-6);

    // Use a random hex string for further uniqueness
    const randomComponent = crypto.randomBytes(2).toString('hex').toUpperCase();
    const doc = this as any
    // Get hotel identifier (use last 3 chars of hotel ID or a default)
    const hotelId = doc.hotel.toString();
    const hotelIdentifier = hotelId.substring(hotelId.length - 3).toUpperCase();

    return `BK-${year}${month}${day}-${hotelIdentifier}-${timestamp}-${randomComponent}`;
};

// OPTIMIZATION 4: Improved pre-save middleware with better guest handling
BookingSchema.pre("save", async function (next) {
    try {
        // Check for overlapping bookings for the same room
        if ((this.isNew || this.isModified("room") || this.isModified("checkIn") || this.isModified("checkOut")) &&
            this.room && this.checkIn && this.checkOut) {

            // Normalize dates for comparison
            const checkInDate = new Date(this.checkIn);
            checkInDate.setHours(0, 0, 0, 0);

            const checkOutDate = new Date(this.checkOut);
            checkOutDate.setHours(23, 59, 59, 999);

            // Query to find overlapping bookings
            const overlappingBookingQuery = {
                _id: {$ne: this._id}, // Exclude current booking (for updates)
                room: this.room,
                status: {$nin: [BookingStatus.CANCELLED, BookingStatus.NO_SHOW]}, // Exclude cancelled bookings
                $or: [
                    // New booking check-in date falls within existing booking
                    {checkIn: {$lte: checkInDate}, checkOut: {$gte: checkInDate}},
                    // New booking check-out date falls within existing booking
                    {checkIn: {$lte: checkOutDate}, checkOut: {$gte: checkOutDate}},
                    // New booking completely encompasses existing booking
                    {checkIn: {$gte: checkInDate}, checkOut: {$lte: checkOutDate}}
                ]
            };

            // Find any overlapping bookings
            const overlappingBooking = await model(constants.DB.BOOKING).findOne(overlappingBookingQuery);

            if (overlappingBooking) {
                const error = new Error(`Room is already booked for the selected dates. Booking ID: ${(overlappingBooking as any).bookingId}`);
                return next(error);
            }
        }

        // Calculate duration nights if checkIn/checkOut dates changed
        if (this.isModified("checkIn") || this.isModified("checkOut") || !this.durationNights) {
            if (!this.checkIn || !this.checkOut) {
                this.durationNights = 0;
            } else {
                this.durationNights = Math.ceil(
                    (this.checkOut?.getTime() - this.checkIn?.getTime()) / (1000 * 3600 * 24)
                );
            }
        }

        // Update payment status if amount or amountPaid changed
        if (this.isModified("amountPaid") || this.isModified("amount")) {
            if (this.amountPaid === 0) {
                this.paymentStatus = PaymentStatus.UNPAID;
            } else if (this.amountPaid >= this.amount) {
                this.paymentStatus = PaymentStatus.FULLY_PAID;
            } else {
                this.paymentStatus = PaymentStatus.PARTIALLY_PAID;
            }
        }

        // Generate booking ID for new bookings
        if (!this.bookingId) {
            this.bookingId = await generateBookingId.call(this);
        }

        // Process guest information
        if (this.isModified("guest") && typeof this.guest !== 'string' && !(this.guest instanceof Types.ObjectId)) {
            const guestDetails = this.guest as any;
            let existingGuest = null;

            // OPTIMIZATION: Use a single query to find guest by email OR phone
            if (guestDetails.email || guestDetails.phoneNumber) {
                const query: any = {$or: []};

                if (guestDetails.email) {
                    query.$or.push({email: guestDetails.email});
                }

                if (guestDetails.phoneNumber) {
                    query.$or.push({phoneNumber: guestDetails.phoneNumber});
                }

                if (query.$or.length > 0) {
                    existingGuest = await User.findOne(query).lean();
                }
            }

            if (existingGuest) {
                this.guest = existingGuest._id as any;
                // Update the user's currentBooking in a non-blocking way
                User.updateOne({_id: existingGuest._id}, {currentBooking: this._id})
                    .exec()
                    .catch(err => console.error('Error updating user currentBooking:', err));
            } else {
                // Create new guest user
                const randomPassword = crypto.randomBytes(8).toString('hex');
                const salt = await bcrypt.genSalt(10);
                const hashedPassword = await bcrypt.hash(randomPassword, salt);

                const newGuest = new User({
                    ...guestDetails,
                    hotel: this.hotel,
                    password: hashedPassword,
                    role: 'guest',
                    currentBooking: this._id
                });

                const savedGuest = await newGuest.save();
                this.guest = savedGuest._id;

                // Send email in a non-blocking way if email provided
                if (guestDetails.email) {
                    // Get hotel name in a non-blocking way
                    model(constants.DB.HOTEL).findById(this.hotel)
                        .select('name')
                        .lean()
                        .then((hotel: any) => {
                            const hotelName = hotel ? hotel.name : 'Our Hotel';
                            return sendNewUserEmail(
                                guestDetails.name || 'Guest',
                                guestDetails.email,
                                randomPassword,
                                hotelName
                            );
                        })
                        .catch(err => console.error('Error sending email to new guest:', err));
                }
            }
        }

        // Apply membership discounts if applicable
        if (this.guest && typeof this.guest !== 'string' && !(this.guest instanceof Types.ObjectId)) {
            if (this.isNew || this.isModified('guest')) {
                const User = model(constants.DB.USER);
                const user = await User.findById(this.guest)
                    .select('membership')
                    .populate('membership', 'discount')
                    .lean() as any;

                if (user && user.membership) {
                    this.membershipDiscount = user.membership?.discount as any;

                    if (this.isNew && !this.originalAmount) {
                        this.originalAmount = this.amount;
                    }

                    const discountAmount = (this.originalAmount || this.amount) * ((this.membershipDiscount || 0) / 100);
                    this.membershipDiscountAmount = discountAmount;

                    if (this.isNew || this.isModified('amount')) {
                        this.amount = (this.originalAmount || this.amount) - discountAmount;
                    }
                }
            }
        }

        next();
    } catch (error) {
        return next(error as any);
    }
});

// OPTIMIZATION 5: Batch transaction processing
// Process all booking-related transactions in a single post-save middleware
// This improves performance by reducing the number of database operations
BookingSchema.post('save', async function (doc: any) {
    try {
        // Collect transactions to be created in bulk
        const transactionsToCreate = [];
        const financialTransactionsToCreate = [];

        const isInitialCreation = doc.isNew ||
            (!doc._previousValues && !doc.isModified());

        // For initial booking creation
        if (isInitialCreation) {
            // Create a transaction record without updating the booking amount
            // since the amount is already set in the booking document
            const booking = await Booking.findById(doc._id)
                .populate('guest')
                .populate('hotel');

            if (booking) {
                // Create the transaction directly without using createTransaction
                // to avoid updating the booking amount again
                const transaction = new Transaction({
                    booking: doc._id,
                    guest: booking.guest._id,
                    hotel: booking.hotel._id,
                    serviceType: 'booking',
                    description: `Room Booking: ${doc.bookingId}`,
                    referenceNo: `BK-${doc._id.toString().slice(-8)}`,
                    debitAmount: 0,
                    creditAmount: doc.amount,
                    balance: doc.amount,
                    createdBy: doc.createdBy
                });

                await transaction.save();

                // Just add the transaction ID to the booking without changing amount
                await Booking.findByIdAndUpdate(doc._id, {
                    $push: {transactions: transaction._id}
                });
            }

            // Add financial transaction
            financialTransactionsToCreate.push({
                hotel: doc.hotel,
                transactionType: TransactionType.INCOME,
                amount: doc.amount,
                description: `Room Booking: ${doc.bookingId}`,
                category: 'Room Booking',
                paymentMethod: doc.paymentMethod === PaymentMethod.ONLINE ?
                    FinancialPaymentMethod.ONLINE : FinancialPaymentMethod.CASH,
                reference: `BK-${doc._id.toString().slice(-8)}`,
                booking: doc._id,
                guest: doc.guest,
                createdBy: doc.createdBy || doc.guest
            });

            // If there's an initial payment, create payment transactions
            // Store the original amountPaid value before it gets modified by transactions
            const initialAmountPaid = doc.amountPaid;
            if (initialAmountPaid > 0) {
                // Create the payment transaction directly without using createTransaction
                // to avoid updating the amountPaid again
                const booking = await Booking.findById(doc._id)
                    .populate('guest')
                    .populate('hotel');

                if (booking) {
                    // Get the last transaction to calculate the new balance
                    const lastTransaction = await Transaction.findOne({booking: doc._id})
                        .sort({date: -1});

                    const previousBalance = lastTransaction ? lastTransaction.balance : 0;
                    const newBalance = previousBalance - initialAmountPaid;

                    const transaction = new Transaction({
                        booking: doc._id,
                        guest: booking.guest._id,
                        hotel: booking.hotel._id,
                        serviceType: 'booking_payment',
                        description: `Initial Payment for Booking: ${doc.bookingId}`,
                        referenceNo: `BKP-${doc._id.toString().slice(-8)}`,
                        debitAmount: initialAmountPaid,
                        creditAmount: 0,
                        balance: newBalance,
                        createdBy: doc.createdBy
                    });

                    await transaction.save();

                    // Just add the transaction ID to the booking without changing amountPaid
                    await Booking.findByIdAndUpdate(doc._id, {
                        $push: {transactions: transaction._id}
                    });
                }

                financialTransactionsToCreate.push({
                    hotel: doc.hotel,
                    transactionType: TransactionType.INCOME,
                    amount: initialAmountPaid,
                    description: `Payment for Booking: ${doc.bookingId}`,
                    category: 'Booking Payment',
                    paymentMethod: doc.paymentMethod === PaymentMethod.ONLINE ?
                        FinancialPaymentMethod.ONLINE : FinancialPaymentMethod.CASH,
                    reference: `BKP-${doc._id.toString().slice(-8)}`,
                    booking: doc._id,
                    guest: doc.guest,
                    createdBy: doc.createdBy || doc.guest
                });
            }
        }
        // For updates to existing bookings
        else {
            // If booking amount was modified
            if (doc.isModified('amount')) {
                const previousAmount = doc._previousValues?.amount || 0;
                const amountDifference = doc.amount - previousAmount;

                if (amountDifference !== 0) {
                    transactionsToCreate.push({
                        bookingId: doc._id,
                        description: `Booking Amount Update: ${doc.bookingId}`,
                        referenceNo: `BKU-${doc._id.toString().slice(-8)}`,
                        creditAmount: amountDifference,
                        serviceType: 'booking_update'
                    });
                }
            }

            // If payment amount was modified
            if (doc.isModified('amountPaid')) {
                const previousPaid = doc._previousValues?.amountPaid || 0;
                const paymentDifference = doc.amountPaid - previousPaid;

                if (paymentDifference !== 0) {
                    transactionsToCreate.push({
                        bookingId: doc._id,
                        description: `Payment Update for Booking: ${doc.bookingId}`,
                        referenceNo: `BKP-${doc._id.toString().slice(-8)}`,
                        debitAmount: paymentDifference,
                        serviceType: 'booking_payment'
                    });
                }
            }
        }

        // Handle status change to cancelled (refund processing)
        if (doc.isModified('status') && doc.status === BookingStatus.CANCELLED && doc.amountPaid > 0) {
            transactionsToCreate.push({
                bookingId: doc._id,
                description: `Refund for Cancelled Booking: ${doc.bookingId}`,
                referenceNo: `BKR-${doc._id.toString().slice(-8)}`,
                debitAmount: doc.amountPaid * -1,
                serviceType: 'booking_refund'
            });
        }

        // OPTIMIZATION: Process all transactions in batch
        // For initial creation, we've already created the transactions directly
        // Only process transactions for updates
        if (!isInitialCreation && transactionsToCreate.length > 0) {
            await Promise.all(transactionsToCreate.map(transaction => createTransaction(transaction)));
        }

        // Always process financial transactions
        if (financialTransactionsToCreate.length > 0) {
            await Promise.all(financialTransactionsToCreate.map(ftransaction => createFinancialTransaction(ftransaction)));
        }

        // OPTIMIZATION: Process membership updates and room status updates in parallel
        const parallelTasks = [];

        // Handle status change to checked-out (upgrade membership)
        if (doc.isModified('status') && doc.status === BookingStatus.CHECKED_OUT) {
            const guestId = doc.guest._id || doc.guest;
            const bookingAmount = doc.amount || 0;

            // Add membership update task
            parallelTasks.push(
                checkAndUpgradeMembership(guestId.toString(), bookingAmount)
            );

            // Add room status update task
            parallelTasks.push(
                roomStatusService.updateRoomStatusOnCheckOut(doc._id.toString())
            );
        }

        // Handle status change to checked-in (update room status)
        if (doc.isModified('status') && doc.status === BookingStatus.CHECKED_IN) {
            parallelTasks.push(
                roomStatusService.updateRoomStatusOnCheckIn(doc._id.toString())
            );
        }

        // Execute all parallel tasks
        if (parallelTasks.length > 0) {
            await Promise.all(parallelTasks);
        }
    } catch (error) {
        console.error('Error in Booking post save middleware:', error);
    }
});

// OPTIMIZATION 6: Optimize findOneAndUpdate handling to reduce database operations
// This middleware now collects all transactions to create in bulk
BookingSchema.pre('findOneAndUpdate', async function () {
    try {
        const update = this.getUpdate() as any;
        const originalDoc = await this.model.findOne(this.getQuery()).select('_id bookingId amount amountPaid status guest hotel room checkIn checkOut').lean() as any;

        if (!originalDoc) return;

        // Check for room availability if updating room, checkIn, or checkOut
        const isUpdatingRoom = update.$set?.room !== undefined || update.room !== undefined;
        const isUpdatingCheckIn = update.$set?.checkIn !== undefined || update.checkIn !== undefined;
        const isUpdatingCheckOut = update.$set?.checkOut !== undefined || update.checkOut !== undefined;

        if (isUpdatingRoom || isUpdatingCheckIn || isUpdatingCheckOut) {
            // Get the updated values or use original values if not being updated
            const roomId = isUpdatingRoom
                ? (update.$set?.room || update.room)
                : originalDoc.room;

            const checkIn = isUpdatingCheckIn
                ? new Date(update.$set?.checkIn || update.checkIn)
                : new Date(originalDoc.checkIn);

            const checkOut = isUpdatingCheckOut
                ? new Date(update.$set?.checkOut || update.checkOut)
                : new Date(originalDoc.checkOut);

            // Normalize dates for comparison
            const checkInDate = new Date(checkIn);
            checkInDate.setHours(0, 0, 0, 0);

            const checkOutDate = new Date(checkOut);
            checkOutDate.setHours(23, 59, 59, 999);

            // Query to find overlapping bookings
            const overlappingBookingQuery = {
                _id: {$ne: originalDoc._id}, // Exclude current booking
                room: roomId,
                status: {$nin: [BookingStatus.CANCELLED, BookingStatus.NO_SHOW]}, // Exclude cancelled bookings
                $or: [
                    // New booking check-in date falls within existing booking
                    {checkIn: {$lte: checkInDate}, checkOut: {$gte: checkInDate}},
                    // New booking check-out date falls within existing booking
                    {checkIn: {$lte: checkOutDate}, checkOut: {$gte: checkOutDate}},
                    // New booking completely encompasses existing booking
                    {checkIn: {$gte: checkInDate}, checkOut: {$lte: checkOutDate}}
                ]
            };

            // Find any overlapping bookings
            const overlappingBooking = await model(constants.DB.BOOKING).findOne(overlappingBookingQuery);

            if (overlappingBooking) {
                // Throw an error to prevent the update
                throw new Error(`Room is already booked for the selected dates. Booking ID: ${(overlappingBooking as any).bookingId}`);
            }
        }

        const transactionsToCreate = [];

        // Handle amount updates
        if (update && (update.$set?.amount !== undefined || update.amount !== undefined)) {
            const newAmount = update.$set?.amount !== undefined ?
                update.$set.amount : update.amount;

            const previousAmount = originalDoc.amount || 0;
            const amountDifference = newAmount - previousAmount;

            if (amountDifference !== 0) {
                transactionsToCreate.push({
                    bookingId: originalDoc._id,
                    description: `Booking Amount Update: ${originalDoc.bookingId}`,
                    referenceNo: `BKU-${originalDoc._id.toString().slice(-8)}`,
                    creditAmount: amountDifference,
                    serviceType: 'booking_update'
                });
            }
        }

        // Handle payment updates
        if (update && (update.$set?.amountPaid !== undefined || update.amountPaid !== undefined)) {
            const newPaymentAmount = update.$set?.amountPaid !== undefined ?
                update.$set.amountPaid : update.amountPaid;

            const previousPayment = originalDoc.amountPaid || 0;
            const paymentDifference = newPaymentAmount - previousPayment;

            if (paymentDifference !== 0) {
                transactionsToCreate.push({
                    bookingId: originalDoc._id,
                    description: `Payment Update for Booking: ${originalDoc.bookingId}`,
                    referenceNo: `BKP-${originalDoc._id.toString().slice(-8)}`,
                    debitAmount: paymentDifference, // Payment from guest (reduces balance)
                    creditAmount: 0,
                    serviceType: 'booking_payment'
                });
            }
        }

        // Handle status change to cancelled
        if (update &&
            ((update.$set?.status === BookingStatus.CANCELLED) ||
                (update.status === BookingStatus.CANCELLED)) &&
            originalDoc.amountPaid > 0 &&
            originalDoc.status !== BookingStatus.CANCELLED) {

            transactionsToCreate.push({
                bookingId: originalDoc._id,
                description: `Refund for Cancelled Booking: ${originalDoc.bookingId}`,
                referenceNo: `BKR-${originalDoc._id.toString().slice(-8)}`,
                debitAmount: originalDoc.amountPaid * -1,
                serviceType: 'booking_refund'
            });
        }

        // Create all transactions in parallel
        if (transactionsToCreate.length > 0) {
            Promise.all(transactionsToCreate.map(transaction => createTransaction(transaction)))
                .catch(err => console.error('Error creating transactions in findOneAndUpdate middleware:', err));
        }

        // Store data for post-update processing
        // @ts-ignore
        this._originalDoc = originalDoc;
        // @ts-ignore
        this._updateData = update;
    } catch (error) {
        console.error('Error in Booking pre findOneAndUpdate middleware:', error);
        throw error; // Re-throw the error to prevent the update
    }
});

// OPTIMIZATION 7: Post findOneAndUpdate middleware for handling membership updates and room status
BookingSchema.post('findOneAndUpdate', async function (doc) {
    try {
        // @ts-ignore
        const originalDoc = this._originalDoc;
        // @ts-ignore
        const update = this._updateData;

        if (!originalDoc || !update || !doc) return;

        const parallelTasks = [];

        // Handle status change to checked-out (upgrade membership)
        if ((update.$set?.status === BookingStatus.CHECKED_OUT || update.status === BookingStatus.CHECKED_OUT) &&
            originalDoc.status !== BookingStatus.CHECKED_OUT) {

            // Get the guest ID and booking amount
            const guestId = originalDoc.guest._id || originalDoc.guest;
            const bookingAmount = originalDoc.amount || 0;

            // Update membership in parallel
            parallelTasks.push(
                checkAndUpgradeMembership(guestId.toString(), bookingAmount)
            );

            // Update room status in parallel
            parallelTasks.push(
                roomStatusService.updateRoomStatusOnCheckOut(originalDoc._id.toString())
            );
        }

        // Handle status change to checked-in (update room status)
        if ((update.$set?.status === BookingStatus.CHECKED_IN || update.status === BookingStatus.CHECKED_IN) &&
            originalDoc.status !== BookingStatus.CHECKED_IN) {

            parallelTasks.push(
                roomStatusService.updateRoomStatusOnCheckIn(originalDoc._id.toString())
            );
        }

        // Execute all tasks in parallel
        if (parallelTasks.length > 0) {
            Promise.all(parallelTasks).catch(err => {
                console.error('Error in post findOneAndUpdate processing:', err);
            });
        }
    } catch (error) {
        console.error('Error in Booking post findOneAndUpdate middleware:', error);
    }
});

// OPTIMIZATION 8: Selective indexing - keep only the most important indexes
BookingSchema.index({status: 1, hotel: 1});
// Add compound index for availability searches
BookingSchema.index({hotel: 1, status: 1, checkIn: 1, checkOut: 1});
// Add TTL index - expire bookings after ~2 years (63072000 seconds)
BookingSchema.index({"createdAt": 1}, {expireAfterSeconds: 63072000});

// Add helper methods for fetching bookings with different population levels
BookingSchema.statics.findWithBasicPopulate = function (query = {}) {
    return this.find({...query, _populateLevel: 'basic'});
};

BookingSchema.statics.findWithFullPopulate = function (query = {}) {
    return this.find({...query, _populateLevel: 'full'});
};

BookingSchema.statics.findWithoutPopulate = function (query = {}) {
    return this.find({...query, _noPopulate: true});
};
export const Booking = model<IBooking>(constants.DB.BOOKING, BookingSchema);