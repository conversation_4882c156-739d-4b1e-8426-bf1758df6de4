import {Types} from "mongoose";
import {Booking} from "../model/booking.model";
import {Transaction} from "../model/transaction.model";

/**
 * Creates a transaction for a booking
 */
export async function createTransaction({
                                            bookingId,
                                            description,
                                            referenceNo,
                                            debitAmount = 0,
                                            creditAmount = 0,
                                            createdBy,
                                            serviceType
                                        }: {
    bookingId: Types.ObjectId;
    description: string;
    referenceNo: string;
    debitAmount?: number;
    creditAmount?: number;
    createdBy?: Types.ObjectId;
    serviceType?: string;
}) {
    // Find the booking with guest and hotel data
    let booking = await Booking.findById(bookingId)
        .populate('guest')
        .populate('hotel') as any;

    if (!booking) {
        throw new Error('Booking not found');
    }

    // Get the last transaction to calculate the new balance
    const lastTransaction = await Transaction.findOne({booking: bookingId})
        .sort({date: -1});

    const previousBalance = lastTransaction ? lastTransaction.balance : 0;
    // For guest transactions:
    // - creditAmount represents charges to guest (increases balance)
    // - debitAmount represents payments from guest (decreases balance)
    const newBalance = previousBalance + creditAmount - debitAmount;

    // Create the transaction
    const transaction = new Transaction({
        booking: bookingId,
        guest: booking.guest._id,
        hotel: booking.hotel._id,
        serviceType, // Optional field to categorize transaction
        description,
        referenceNo,
        debitAmount,
        creditAmount,
        balance: newBalance,
        createdBy
    });

    await transaction.save();
    // Update the booking
    // creditAmount represents charges to the guest (increases amount)
    // debitAmount represents payments from the guest (increases amountPaid)

    // Check if this is a new booking (created within the last 5 seconds)
    booking = await Booking.findById(bookingId);
    const isNewBooking = booking &&
        (new Date().getTime() - new Date(booking.createdAt).getTime() < 5000);

    if (isNewBooking) {
        // For new bookings, just add the transaction ID without changing amount/amountPaid
        // because these values are already set in the booking document
        await Booking.findByIdAndUpdate(bookingId, {
            $push: {transactions: transaction._id}
        });
    } else {
        // For existing bookings, update the amount and amountPaid
        await Booking.findByIdAndUpdate(bookingId, {
            $push: {transactions: transaction._id},
            $inc: {amount: creditAmount, amountPaid: debitAmount}
        });
    }

    return transaction;
}