import {z} from "zod";

// MongoDB ObjectId regex
const objectIdRegex = /^[a-f\d]{24}$/i;

// Payment validation schema
export const makePaymentSchema = z.object({
    bookingId: z
        .string()
        .regex(objectIdRegex, "Invalid booking ID format"),
    amount: z
        .number()
        .positive("Payment amount must be positive")
        .max(999999, "Payment amount too large"),
    paymentMethod: z
        .enum(["cash", "online"], {
            errorMap: () => ({message: "Payment method must be either 'cash' or 'online'"})
        }),
    notes: z
        .string()
        .optional()
        .transform(val => val?.trim())
});

// Get payment history schema
export const getPaymentHistorySchema = z.object({
    bookingId: z
        .string()
        .regex(objectIdRegex, "Invalid booking ID format")
});

// Get booking balance schema
export const getBookingBalanceSchema = z.object({
    bookingId: z
        .string()
        .regex(objectIdRegex, "Invalid booking ID format")
});

export type MakePaymentInput = z.infer<typeof makePaymentSchema>;
export type GetPaymentHistoryInput = z.infer<typeof getPaymentHistorySchema>;
export type GetBookingBalanceInput = z.infer<typeof getBookingBalanceSchema>;
